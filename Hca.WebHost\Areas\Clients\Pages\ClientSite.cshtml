@page "/clients/{clientId:guid}/sites/{id}"
@model Hca.WebHost.Areas.Clients.Pages.ClientSiteModel
@{
    var isNew = Model.Id.ToLower() == "new";
    var breadcrumbName = isNew ? "New" : Model.Site.SiteName;
    var yesNoItems = new[] { new SelectListItem("No", "false"), new SelectListItem("Yes", "true") };

    Layout = isNew ? "_LayoutClient" : "_LayoutSite";
    ViewData["Client"] = Model.Client;
    ViewData["Site"] = Model.Site;
    ViewData["PlanId"] = Guid.Empty;
    ViewData["showMapKey"] = false;

    var mapScriptsModel = new _MapScriptsModel(Model.Site, Model.Address)
    {
        PropertyCount = Model.Site.PropertyCount,
        ShowMapNumbers = true,
        PointerColour = "#68BBE3",
    };
}

<div class="row">
    <div class="col">
        <form method="post" id="siteForm">
            <div class="card card-default">
                <div class="card-body">
                    <input type="hidden" asp-for="Site.Id" />
                    <input type="hidden" asp-for="Site.AddressId" />
                    <input asp-for="Site.SiteName" row-label="Site Name" />
                    <partial name="_AddressFormPartial" for="Address" />
                </div>
                <save-cancel-footer EditButton="false"
                                    CancelUrl="@Urls.ClientSites(Model.Client.Id)"></save-cancel-footer>
            </div>
        </form>
    </div>
</div>

@section scripts{
    <partial name="_MapScripts" model="mapScriptsModel" />

    <script>

        $("#siteForm").submit(function (e) {
            e.preventDefault();

            var frm = this;
            var callback = () => { frm.submit(); };

            findCoordinates({
                success: callback,
                fail: callback,
            });

            return false;
        });

        $(() => {
            window.initMap = initMap;

            drawBreadcrumb([
                
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { url: '@Urls.ClientSites(Model.Client.Id)', text: 'Sites' },
                { text: '@breadcrumbName' },
            ]);
    });

    </script>
}