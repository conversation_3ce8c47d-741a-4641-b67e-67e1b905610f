using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientSitesModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientSitesModel(
        ClientService clientService,
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    public IEnumerable<SiteDtoExtended> Sites { get; private set; }
    public IEnumerable<SiteDtoExtended> ArchivedSites { get; private set; }
    public ClientDto Client { get; private set; }

    [BindProperty]
    public bool ShowArchivedSites { get; set; }

    [BindProperty]
    public string SearchText { get; set; }

    public async Task<IActionResult> OnGetAsync(Guid clientId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Client = await _clientService.GetClientAsync(clientId, cancellationToken);

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(Guid clientId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Client = await _clientService.GetClientAsync(clientId, cancellationToken);
        Sites = (await _clientService.GetSitesAsync(clientId, SearchText, cancellationToken)).Items;

        if (ShowArchivedSites)
        {
            ArchivedSites = (await _clientService.GetSitesAsync(clientId, SearchText, true, cancellationToken)).Items;
        }

        return Partial("~/Areas/Clients/Pages/_ClientSiteSearchResults.cshtml", this);
    }
}
