@model Hca.WebHost.Areas.Clients.Pages._ListMapScriptsModel
@{
    string CalculatePointerColour(_ListMapScriptsModel.MapModel address)
    {
        if (!Model.PointerColour.IsNullOrWhiteSpace()) return Model.PointerColour;

        return address.NextInspection switch
        {
            null => "#768294",
            DateTime dt when dt < DateTime.Now => "#f05050",
            DateTime dt when dt < DateTime.Now.AddMonths(1) => "#ff902b",
            _ => "#27c24c",
        };
    }
}

<script>
    var locations = [
    @{
        foreach (var address in Model.MapModels.Where(p => p.Lat.HasValue && p.Lon.HasValue))
        {
            <text>
                [
                    '@address.DisplayText',
                    @(address.Lat),
                    @(address.Lon),
                    '@address.DirectUrl',
                    '@CalculatePointerColour(address)',
                    @(address.PropertyCount)
                ],
            </text>
        }
    }
        ];
    var markers = [];

    function initMap() {
        var map = new google.maps.Map(
            document.getElementById("map"),
            {
                // maxZoom: 16,
                gestureHandling: 'greedy',
                center: new google.maps.LatLng(52.736129, -1.988229),
                mapTypeId: google.maps.MapTypeId.ROADMAP
            });
        var bounds = new google.maps.LatLngBounds();
        var infowindow = new google.maps.InfoWindow();
        var calloutSVGFilled = "M25 1 h-25 v20 h7.547 l5.439 8.05 5.44 -8.05 h7 z";
        var pinSVGFilled = "M 12,2 C 8.1340068,2 5,5.1340068 5,9 c 0,5.25 7,13 7,13 0,0 7,-7.75 7,-13 0,-3.8659932 -3.134007,-7 -7,-7 z";

        // for each location
        for (i = 0; i < locations.length; i++) {
            var latlng = { lat: locations[i][1], lng: locations[i][2] };
            map.setCenter(latlng);
            var marker = new google.maps.Marker({
                map: map,
                title: locations[i][0],
                url: locations[i][3],
                position: latlng,
                icon: {
                    path: @Html.Raw(Model.ShowMapNumbers ? "calloutSVGFilled" : "pinSVGFilled"),
                    fillOpacity: 1,
                    fillColor: locations[i][4],
                    labelOrigin: new google.maps.Point(12, 11),
                    anchor: new google.maps.Point(12.5, 25),
                },
    @if (Model.ShowMapNumbers)
    {
        <text>
                    label: {
                    text: `${locations[i][5]}`,
                    fontWeight: 'bold',
                },
        </text>
    }
                });

        //if (locations.length > 1) {
        bounds.extend(marker.getPosition());
        map.fitBounds(bounds);
        //}

        google.maps.event.addListener(marker, 'click', function () {
            window.location.href = this.url;
        });

        google.maps.event.addListener(marker, 'hover', (function (marker, location) {
            return function () {
                infowindow.setContent(location[0]);
                infowindow.open(map, marker);
            };
        })(marker, location));

        markers.push(marker);
    }

            //now fit the map to the newly inclusive bounds
            //map.fitBounds(bounds);
        }

</script>

<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCurPCz8HEXfT3pkLqpBprIKfImFtkE3DQ&callback=initMap&v=weekly"
        defer></script>